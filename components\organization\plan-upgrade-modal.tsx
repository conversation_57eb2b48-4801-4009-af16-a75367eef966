'use client'

import React from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  Crown, 
  Star, 
  Sparkles, 
  Check, 
  Zap,
  Building2,
  Users,
  BarChart3,
  Headphones,
  Globe,
  Shield,
  Rocket
} from 'lucide-react'

interface PlanUpgradeModalProps {
  isOpen: boolean
  onClose: () => void
  currentPlan: string
}

const plans = [
  {
    id: 'starter',
    name: 'Starter',
    description: 'Perfect for single restaurant owners',
    price: 29,
    icon: Sparkles,
    color: 'from-blue-500 to-blue-600',
    borderColor: 'border-blue-200',
    bgColor: 'bg-blue-50',
    textColor: 'text-blue-600',
    restaurants: 1,
    features: [
      '1 Restaurant Location',
      'Up to 50 Menu Items',
      'Custom Subdomain',
      'Basic Analytics',
      'Email Support',
      'Mobile Responsive Design'
    ]
  },
  {
    id: 'professional',
    name: 'Professional',
    description: 'Most popular for growing restaurants',
    price: 79,
    icon: Star,
    color: 'from-purple-500 to-purple-600',
    borderColor: 'border-purple-200',
    bgColor: 'bg-purple-50',
    textColor: 'text-purple-600',
    restaurants: 3,
    popular: true,
    features: [
      'Up to 3 Restaurant Locations',
      'Unlimited Menu Items',
      'Custom Domain',
      'Advanced Analytics',
      'Team Management',
      'Priority Support',
      'Multi-location Dashboard',
      'Inventory Management'
    ]
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    description: 'For restaurant chains and franchises',
    price: 199,
    icon: Crown,
    color: 'from-orange-500 to-red-500',
    borderColor: 'border-orange-200',
    bgColor: 'bg-gradient-to-br from-orange-50 to-red-50',
    textColor: 'text-orange-600',
    restaurants: 999,
    features: [
      'Unlimited Restaurant Locations',
      'White-label Solution',
      'API Access',
      'Custom Integrations',
      'Dedicated Account Manager',
      '24/7 Phone Support',
      'Advanced Reporting',
      'Multi-tenant Architecture',
      'Custom Branding'
    ]
  }
]

export function PlanUpgradeModal({ isOpen, onClose, currentPlan }: PlanUpgradeModalProps) {
  const handleUpgrade = (planId: string) => {
    // TODO: Implement Stripe checkout or upgrade flow
    console.log('Upgrading to plan:', planId)
    // For now, just close the modal
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="text-center space-y-4 pb-6">
          <DialogTitle className="text-3xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
            Upgrade Your Plan
          </DialogTitle>
          <DialogDescription className="text-lg text-gray-600">
            Unlock more restaurant locations and premium features
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {plans.map((plan) => {
            const Icon = plan.icon
            const isCurrentPlan = currentPlan === plan.id
            const isUpgrade = !isCurrentPlan

            return (
              <Card 
                key={plan.id}
                className={`relative overflow-hidden transition-all duration-300 hover:shadow-xl ${
                  plan.popular ? 'ring-2 ring-purple-500 scale-105' : ''
                } ${isCurrentPlan ? 'opacity-60' : ''}`}
              >
                {plan.popular && (
                  <div className="absolute top-0 left-0 right-0 bg-gradient-to-r from-purple-500 to-purple-600 text-white text-center py-2 text-sm font-medium">
                    Most Popular
                  </div>
                )}
                
                <CardHeader className={`${plan.bgColor} ${plan.popular ? 'pt-12' : 'pt-6'}`}>
                  <div className="flex items-center justify-center mb-4">
                    <div className={`h-16 w-16 rounded-full bg-gradient-to-r ${plan.color} flex items-center justify-center shadow-lg`}>
                      <Icon className="h-8 w-8 text-white" />
                    </div>
                  </div>
                  
                  <CardTitle className="text-center text-2xl font-bold">
                    {plan.name}
                  </CardTitle>
                  <CardDescription className="text-center">
                    {plan.description}
                  </CardDescription>
                  
                  <div className="text-center pt-4">
                    <div className="flex items-baseline justify-center">
                      <span className="text-4xl font-bold">${plan.price}</span>
                      <span className="text-gray-500 ml-1">/month</span>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="p-6 space-y-6">
                  <div className="text-center">
                    <div className={`inline-flex items-center space-x-2 px-4 py-2 rounded-full ${plan.bgColor} ${plan.borderColor} border`}>
                      <Building2 className={`h-4 w-4 ${plan.textColor}`} />
                      <span className={`font-medium ${plan.textColor}`}>
                        {plan.restaurants === 999 ? 'Unlimited' : plan.restaurants} Restaurant{plan.restaurants !== 1 ? 's' : ''}
                      </span>
                    </div>
                  </div>

                  <div className="space-y-3">
                    {plan.features.map((feature, index) => (
                      <div key={index} className="flex items-center space-x-3">
                        <div className={`h-5 w-5 rounded-full bg-gradient-to-r ${plan.color} flex items-center justify-center flex-shrink-0`}>
                          <Check className="h-3 w-3 text-white" />
                        </div>
                        <span className="text-sm text-gray-700">{feature}</span>
                      </div>
                    ))}
                  </div>

                  <div className="pt-4">
                    {isCurrentPlan ? (
                      <Button 
                        disabled 
                        className="w-full"
                        variant="outline"
                      >
                        Current Plan
                      </Button>
                    ) : (
                      <Button 
                        onClick={() => handleUpgrade(plan.id)}
                        className={`w-full bg-gradient-to-r ${plan.color} hover:opacity-90 text-white shadow-lg`}
                      >
                        <Rocket className="h-4 w-4 mr-2" />
                        Upgrade to {plan.name}
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        <div className="mt-8 p-6 bg-gray-50 rounded-lg">
          <div className="text-center space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">
              Why Upgrade?
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
              <div className="flex items-center space-x-2">
                <Building2 className="h-4 w-4 text-orange-500" />
                <span>Manage multiple restaurant locations</span>
              </div>
              <div className="flex items-center space-x-2">
                <BarChart3 className="h-4 w-4 text-orange-500" />
                <span>Advanced analytics and reporting</span>
              </div>
              <div className="flex items-center space-x-2">
                <Headphones className="h-4 w-4 text-orange-500" />
                <span>Priority customer support</span>
              </div>
            </div>
            <p className="text-xs text-gray-500">
              All plans include a 14-day free trial. Cancel anytime.
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
