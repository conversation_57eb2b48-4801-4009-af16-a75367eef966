'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  Building2, 
  Plus, 
  MapPin, 
  Phone, 
  Mail, 
  Calendar,
  Crown,
  Zap,
  Sparkles,
  Star,
  Users,
  Globe,
  ChefHat,
  Clock,
  TrendingUp
} from 'lucide-react'
import { PlanUpgradeModal } from './plan-upgrade-modal'
import { AddRestaurantModal } from './add-restaurant-modal'

interface Organization {
  id: string
  name: string
  slug: string
  description?: string
  restaurant_type: string
  contact_email?: string
  phone?: string
  address?: string
  city?: string
  state?: string
  country: string
  postal_code?: string
  subscription_status: string
  plan_id?: string
  trial_ends_at?: string
  created_at: string
  updated_at: string
}

interface RestaurantManagementProps {
  organization: Organization
  userRole: string
  currentPlan: string
  planLimits: {
    restaurants: number
    name: string
  }
}

const restaurantTypeLabels = {
  fast_food: 'Fast Food',
  fine_dining: 'Fine Dining',
  casual_dining: 'Casual Dining',
  cafe: 'Café',
  bakery: 'Bakery',
  food_truck: 'Food Truck',
  buffet: 'Buffet',
  bar_grill: 'Bar & Grill',
  other: 'Other'
}

export function RestaurantManagement({ 
  organization, 
  userRole, 
  currentPlan, 
  planLimits 
}: RestaurantManagementProps) {
  const [showUpgradeModal, setShowUpgradeModal] = useState(false)
  const [showAddRestaurantModal, setShowAddRestaurantModal] = useState(false)

  const canManage = ['owner', 'admin'].includes(userRole)
  const isTrialOrFree = currentPlan === 'trial'
  const currentRestaurantCount = 1 // For now, showing current organization as 1 restaurant
  const canAddMore = currentRestaurantCount < planLimits.restaurants

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const getPlanIcon = (plan: string) => {
    switch (plan) {
      case 'trial': return <Clock className="h-4 w-4" />
      case 'starter': return <Sparkles className="h-4 w-4" />
      case 'professional': return <Star className="h-4 w-4" />
      case 'enterprise': return <Crown className="h-4 w-4" />
      default: return <Clock className="h-4 w-4" />
    }
  }

  const getPlanColor = (plan: string) => {
    switch (plan) {
      case 'trial': return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'starter': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'professional': return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'enterprise': return 'bg-gradient-to-r from-orange-100 to-red-100 text-orange-800 border-orange-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  return (
    <div className="space-y-8">
      {/* Current Plan Status */}
      <Card className="border-2 border-dashed border-orange-200 bg-gradient-to-br from-orange-50 to-red-50">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {getPlanIcon(currentPlan)}
              <div>
                <CardTitle className="text-lg">Current Plan: {planLimits.name}</CardTitle>
                <CardDescription>
                  {currentRestaurantCount} of {planLimits.restaurants === 999 ? 'unlimited' : planLimits.restaurants} restaurants
                </CardDescription>
              </div>
            </div>
            <Badge className={`${getPlanColor(currentPlan)} border`}>
              {organization.subscription_status === 'trial' ? 'Free Trial' : planLimits.name}
            </Badge>
          </div>
        </CardHeader>
        {isTrialOrFree && organization.trial_ends_at && (
          <CardContent>
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <Clock className="h-5 w-5 text-amber-600" />
                <div>
                  <p className="text-sm font-medium text-amber-800">
                    Trial ends on {formatDate(organization.trial_ends_at)}
                  </p>
                  <p className="text-xs text-amber-600">
                    Upgrade to continue using all features
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Current Restaurant */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold flex items-center space-x-2">
            <Building2 className="h-6 w-6 text-orange-600" />
            <span>Your Restaurants</span>
          </h2>
          <div className="text-sm text-gray-500">
            {currentRestaurantCount} of {planLimits.restaurants === 999 ? '∞' : planLimits.restaurants}
          </div>
        </div>

        {/* Main Restaurant Card */}
        <Card className="overflow-hidden border-2 border-orange-100 shadow-lg hover:shadow-xl transition-all duration-300">
          <div className="bg-gradient-to-r from-orange-500 to-red-500 h-2"></div>
          <CardHeader className="pb-4">
            <div className="flex items-start justify-between">
              <div className="flex items-center space-x-4">
                <div className="h-16 w-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center shadow-lg">
                  <ChefHat className="h-8 w-8 text-white" />
                </div>
                <div>
                  <CardTitle className="text-xl text-gray-900">{organization.name}</CardTitle>
                  <CardDescription className="text-base">
                    {restaurantTypeLabels[organization.restaurant_type as keyof typeof restaurantTypeLabels]}
                  </CardDescription>
                  <div className="flex items-center space-x-2 mt-2">
                    <Badge variant="outline" className="text-xs">
                      <Globe className="h-3 w-3 mr-1" />
                      {organization.slug}.yourdomain.com
                    </Badge>
                  </div>
                </div>
              </div>
              <Badge className="bg-green-100 text-green-800 border-green-200">
                Active
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {organization.description && (
              <p className="text-gray-600 text-sm">{organization.description}</p>
            )}
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {organization.contact_email && (
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Mail className="h-4 w-4 text-orange-500" />
                  <span>{organization.contact_email}</span>
                </div>
              )}
              {organization.phone && (
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Phone className="h-4 w-4 text-orange-500" />
                  <span>{organization.phone}</span>
                </div>
              )}
              {(organization.address || organization.city) && (
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <MapPin className="h-4 w-4 text-orange-500" />
                  <span>
                    {[organization.address, organization.city, organization.state]
                      .filter(Boolean)
                      .join(', ')}
                  </span>
                </div>
              )}
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Calendar className="h-4 w-4 text-orange-500" />
                <span>Created {formatDate(organization.created_at)}</span>
              </div>
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4 text-sm text-gray-500">
                <div className="flex items-center space-x-1">
                  <Users className="h-4 w-4" />
                  <span>Team: {userRole}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <TrendingUp className="h-4 w-4" />
                  <span>Growing</span>
                </div>
              </div>
              {canManage && (
                <Button variant="outline" size="sm">
                  Manage Settings
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Add Restaurant Section */}
      <Card className="border-2 border-dashed border-gray-200 bg-gray-50/50">
        <CardContent className="p-8 text-center">
          <div className="max-w-md mx-auto space-y-4">
            <div className="h-16 w-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center mx-auto">
              <Plus className="h-8 w-8 text-gray-400" />
            </div>
            
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Add Another Restaurant</h3>
              <p className="text-sm text-gray-600 mt-1">
                {canAddMore 
                  ? 'Expand your business with additional restaurant locations'
                  : `Upgrade your plan to add more restaurants (${planLimits.restaurants === 999 ? 'unlimited' : planLimits.restaurants} max)`
                }
              </p>
            </div>

            {canAddMore ? (
              <Button 
                onClick={() => setShowAddRestaurantModal(true)}
                className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600"
                disabled={!canManage}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Restaurant
              </Button>
            ) : (
              <Button 
                onClick={() => setShowUpgradeModal(true)}
                variant="outline"
                className="border-orange-200 text-orange-600 hover:bg-orange-50"
              >
                <Crown className="h-4 w-4 mr-2" />
                Upgrade Plan
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Modals */}
      <PlanUpgradeModal 
        isOpen={showUpgradeModal}
        onClose={() => setShowUpgradeModal(false)}
        currentPlan={currentPlan}
      />
      
      <AddRestaurantModal 
        isOpen={showAddRestaurantModal}
        onClose={() => setShowAddRestaurantModal(false)}
        organizationId={organization.id}
      />
    </div>
  )
}
