import { requireAuth } from '@/lib/auth'
import { createClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import { RestaurantManagement } from '@/components/organization/restaurant-management'

export default async function OrganizationPage() {
  // This will redirect to login if not authenticated
  const user = await requireAuth()

  // Fetch user's organization data
  const supabase = createClient()
  const { data: userData } = await supabase
    .from('users')
    .select(`
      organization_id,
      role,
      organizations (
        id,
        name,
        slug,
        description,
        restaurant_type,
        contact_email,
        phone,
        address,
        city,
        state,
        country,
        postal_code,
        subscription_status,
        plan_id,
        trial_ends_at,
        created_at,
        updated_at
      )
    `)
    .eq('id', user.id)
    .single()

  if (!userData?.organizations) {
    redirect('/onboarding/organization')
  }

  const organization = userData.organizations
  const userRole = userData.role

  // Determine current plan and limits
  const getCurrentPlan = () => {
    if (organization.subscription_status === 'trial') return 'trial'
    return organization.plan_id || 'trial'
  }

  const currentPlan = getCurrentPlan()

  // Plan limits for restaurants
  const planLimits = {
    trial: { restaurants: 1, name: 'Free Trial' },
    starter: { restaurants: 1, name: 'Starter' },
    professional: { restaurants: 3, name: 'Professional' },
    enterprise: { restaurants: 999, name: 'Enterprise' }
  }

  const currentLimit = planLimits[currentPlan as keyof typeof planLimits] || planLimits.trial

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
            Organization Management
          </h1>
          <p className="text-gray-600 mt-2">
            Manage your restaurants and organization settings
          </p>
        </div>
      </div>

      <RestaurantManagement 
        organization={organization}
        userRole={userRole}
        currentPlan={currentPlan}
        planLimits={currentLimit}
      />
    </div>
  )
}
