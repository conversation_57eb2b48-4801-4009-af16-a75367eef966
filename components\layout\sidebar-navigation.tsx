'use client'

import React, { useState, createContext, useContext } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import {
  ChevronLeft,
  ChevronRight,
  Home,
  Users,
  Settings,
  Menu,
  X,
  Store,
  CreditCard,
  UserCheck,
  Shield,
  LogOut,
  Building,
  Database,
  LogIn,
  Key,
  Globe,
  Layers,
  Monitor,
  Rocket,
  Code,
  AlertTriangle
} from 'lucide-react'

// Context for sidebar state
const SidebarContext = createContext<{
  isCollapsed: boolean
  setIsCollapsed: (collapsed: boolean) => void
  isMobileOpen: boolean
  setIsMobileOpen: (open: boolean) => void
}>({
  isCollapsed: false,
  setIsCollapsed: () => {},
  isMobileOpen: false,
  setIsMobileOpen: () => {}
})

export const useSidebar = () => useContext(SidebarContext)

// Navigation items configuration
export const getNavigationItems = () => [
  // Main Dashboard
  {
    title: 'Dashboard',
    href: '/dashboard/overview',
    icon: Home,
    badge: null,
    group: 'main',
    tooltip: 'Main dashboard overview'
  },

  // Organization Management
  {
    title: 'Organization',
    href: '/dashboard/organization',
    icon: Store,
    badge: null,
    group: 'organization',
    tooltip: 'Organization settings and management'
  },
  {
    title: 'Multi-Tenant',
    href: '/dashboard/organization/multi-tenant',
    icon: Building,
    badge: null,
    group: 'organization',
    tooltip: 'Multi-tenant configuration'
  },

  // Team & Access Management
  {
    title: 'Team Management',
    href: '/dashboard/team',
    icon: UserCheck,
    badge: null,
    group: 'team',
    tooltip: 'Manage team members'
  },
  {
    title: 'Roles & Permissions',
    href: '/dashboard/roles',
    icon: Shield,
    badge: null,
    group: 'team',
    tooltip: 'Configure user roles and permissions'
  },

  // Settings & Configuration
  {
    title: 'Settings',
    href: '/dashboard/settings',
    icon: Settings,
    badge: null,
    group: 'settings',
    tooltip: 'Application settings and preferences'
  },

  // Development & Testing (only show in development)
  ...(process.env.NODE_ENV === 'development' ? [
    {
      title: 'Database Test',
      href: '/test-db',
      icon: Database,
      badge: 'Dev',
      group: 'development',
      tooltip: 'Test database connections and queries'
    },
    {
      title: 'Login Test',
      href: '/test-login',
      icon: LogIn,
      badge: 'Dev',
      group: 'development',
      tooltip: 'Test authentication flows'
    },
    {
      title: 'RBAC Test',
      href: '/test-rbac',
      icon: Key,
      badge: 'Dev',
      group: 'development',
      tooltip: 'Test role-based access control'
    },
    {
      title: 'Tenant Test',
      href: '/test-tenant',
      icon: Globe,
      badge: 'Dev',
      group: 'development',
      tooltip: 'Test multi-tenant functionality'
    }
  ] : []),

  // UI Components & Demos
  {
    title: 'Sidebar Gallery',
    href: '/sidebar-demo',
    icon: Layers,
    badge: 'New',
    group: 'demos',
    tooltip: 'Explore sidebar variants and designs'
  },
  {
    title: 'Basic Sidebars',
    href: '/sidebar-demo/basic',
    icon: Monitor,
    badge: null,
    group: 'demos',
    tooltip: 'Basic sidebar collection'
  },
  {
    title: 'Advanced Sidebars',
    href: '/sidebar-demo/advanced',
    icon: Rocket,
    badge: null,
    group: 'demos',
    tooltip: 'Advanced sidebar collection with premium features'
  },
  {
    title: 'Integration Guide',
    href: '/sidebar-demo/integration',
    icon: Code,
    badge: null,
    group: 'demos',
    tooltip: 'Learn how to integrate sidebar components'
  },

  // System Pages
  {
    title: 'Subscription Required',
    href: '/subscription-required',
    icon: CreditCard,
    badge: null,
    group: 'system',
    tooltip: 'Subscription upgrade page'
  },
  {
    title: 'Unauthorized',
    href: '/unauthorized',
    icon: AlertTriangle,
    badge: null,
    group: 'system',
    tooltip: 'Access denied page'
  }
]

// Default navigation items for backward compatibility
export const navigationItems = getNavigationItems()

// Group configurations
export const navigationGroups = {
  main: { title: 'Dashboard', icon: Home },
  organization: { title: 'Organization', icon: Building },
  team: { title: 'Team & Access', icon: Users },
  settings: { title: 'Settings', icon: Settings },
  development: { title: 'Development', icon: Code },
  demos: { title: 'UI Demos', icon: Layers },
  system: { title: 'System', icon: AlertTriangle }
}

interface SidebarProviderProps {
  children: React.ReactNode
  defaultCollapsed?: boolean
}

export function SidebarProvider({ children, defaultCollapsed = false }: SidebarProviderProps) {
  // Load collapsed state from localStorage
  const [isCollapsed, setIsCollapsed] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('sidebar-collapsed')
      return saved !== null ? JSON.parse(saved) : defaultCollapsed
    }
    return defaultCollapsed
  })
  const [isMobileOpen, setIsMobileOpen] = useState(false)

  // Persist collapsed state to localStorage
  const handleSetIsCollapsed = (collapsed: boolean) => {
    setIsCollapsed(collapsed)
    if (typeof window !== 'undefined') {
      localStorage.setItem('sidebar-collapsed', JSON.stringify(collapsed))
    }
  }

  return (
    <SidebarContext.Provider value={{
      isCollapsed,
      setIsCollapsed: handleSetIsCollapsed,
      isMobileOpen,
      setIsMobileOpen
    }}>
      {children}
    </SidebarContext.Provider>
  )
}

interface SidebarProps {
  variant?: 'default' | 'modern' | 'minimal' | 'glass' | 'gradient'
  className?: string
}

export function Sidebar({ variant = 'default', className }: SidebarProps) {
  const { isCollapsed, setIsCollapsed, isMobileOpen, setIsMobileOpen } = useSidebar()
  const pathname = usePathname()

  const sidebarVariants = {
    default: 'bg-white border-r border-gray-200 shadow-sm fixed inset-y-0',
    modern: 'bg-slate-900 border-r border-slate-800 fixed inset-y-0',
    minimal: 'bg-gray-50 border-r border-gray-100 fixed inset-y-0',
    glass: 'bg-white/95 backdrop-blur-xl border-r border-white/20 shadow-xl fixed inset-y-0',
    gradient: 'bg-gradient-to-b from-orange-500 via-red-500 to-rose-600 border-r border-orange-400/20 fixed inset-y-0'
  }

  const textVariants = {
    default: 'text-gray-900',
    modern: 'text-white',
    minimal: 'text-gray-700',
    glass: 'text-gray-900',
    gradient: 'text-white'
  }

  const hoverVariants = {
    default: 'hover:bg-gray-100',
    modern: 'hover:bg-slate-800',
    minimal: 'hover:bg-white',
    glass: 'hover:bg-white/60',
    gradient: 'hover:bg-white/20'
  }

  const activeVariants = {
    default: 'bg-orange-50 text-orange-600 border-r-2 border-orange-600',
    modern: 'bg-slate-800 text-orange-400 border-r-2 border-orange-400',
    minimal: 'bg-white text-orange-600 shadow-sm border-r-2 border-orange-600',
    glass: 'bg-white/90 text-orange-600 border-r-2 border-orange-600',
    gradient: 'bg-white/30 text-white border-r-2 border-white'
  }

  return (
    <TooltipProvider>
      {/* Mobile backdrop */}
      {isMobileOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={() => setIsMobileOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div
        className={cn(
          'z-50 h-screen transition-all duration-300 ease-in-out flex flex-col',
          isCollapsed ? 'w-16' : 'w-64',
          isMobileOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0',
          sidebarVariants[variant],
          className,
          'lg:relative lg:translate-x-0'
        )}
      >
        {/* Header */}
        <div className="flex h-16 items-center justify-between px-4 border-b border-current/10">
          {!isCollapsed && (
            <div className="flex items-center space-x-2">
              <div className={cn(
                'h-8 w-8 rounded-lg flex items-center justify-center',
                variant === 'gradient' ? 'bg-white/20' : 'bg-gradient-to-r from-orange-500 to-red-500'
              )}>
                <span className={cn(
                  'font-bold text-sm',
                  variant === 'gradient' ? 'text-white' : 'text-white'
                )}>R</span>
              </div>
              <span className={cn(
                'font-bold text-lg truncate',
                textVariants[variant]
              )}>
                RestaurantSaaS
              </span>
            </div>
          )}
          
          {/* Toggle buttons */}
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsCollapsed(!isCollapsed)}
              className={cn(
                'hidden lg:flex h-8 w-8 p-0',
                variant === 'gradient' ? 'hover:bg-white/20 text-white' : ''
              )}
            >
              {isCollapsed ? (
                <ChevronRight className="h-4 w-4" />
              ) : (
                <ChevronLeft className="h-4 w-4" />
              )}
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMobileOpen(false)}
              className={cn(
                'lg:hidden h-8 w-8 p-0',
                variant === 'gradient' ? 'hover:bg-white/20 text-white' : ''
              )}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 overflow-y-auto p-4 space-y-6">
          {Object.entries(navigationGroups).map(([groupKey, group], groupIndex) => {
            const groupItems = navigationItems.filter(item => item.group === groupKey)

            // Don't render empty groups
            if (groupItems.length === 0) return null

            return (
              <div key={groupKey}>
                {!isCollapsed && (
                  <div className="flex items-center space-x-2 mb-3">
                    <group.icon className={cn('h-4 w-4', textVariants[variant])} />
                    <span className={cn(
                      'text-xs font-semibold uppercase tracking-wider',
                      textVariants[variant],
                      'opacity-70'
                    )}>
                      {group.title}
                    </span>
                  </div>
                )}

                <div className="space-y-1">
                  {groupItems.map((item) => {
                    const isActive = pathname === item.href
                    const Icon = item.icon

                    const linkContent = (
                      <Link
                        href={item.href}
                        className={cn(
                          'flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200',
                          isActive ? activeVariants[variant] : cn(hoverVariants[variant], textVariants[variant]),
                          isCollapsed && 'justify-center px-2'
                        )}
                      >
                        <Icon className="h-5 w-5 flex-shrink-0" />
                        {!isCollapsed && (
                          <>
                            <span className="flex-1 truncate">{item.title}</span>
                            {item.badge && (
                              <Badge
                                variant={isActive ? "default" : "secondary"}
                                className="h-5 text-xs"
                              >
                                {item.badge}
                              </Badge>
                            )}
                          </>
                        )}
                      </Link>
                    )

                    return isCollapsed ? (
                      <Tooltip key={item.href}>
                        <TooltipTrigger asChild>
                          {linkContent}
                        </TooltipTrigger>
                        <TooltipContent side="right" className="ml-2">
                          <p>{item.tooltip || item.title}</p>
                        </TooltipContent>
                      </Tooltip>
                    ) : (
                      <div key={item.href}>
                        {linkContent}
                      </div>
                    )
                  })}
                </div>

                {!isCollapsed && groupIndex < Object.keys(navigationGroups).length - 1 && (
                  <Separator className="my-4 opacity-30" />
                )}
              </div>
            )
          })}
        </nav>

        {/* Footer */}
        <div className="border-t border-current/10 p-4">
          <div className="flex items-center space-x-3">
            {!isCollapsed && (
              <>
                <div className="h-8 w-8 rounded-full bg-gradient-to-r from-orange-400 to-red-400 flex items-center justify-center">
                  <span className="text-white text-sm font-medium">JD</span>
                </div>
                <div className="flex-1 min-w-0">
                  <p className={cn('text-sm font-medium truncate', textVariants[variant])}>
                    John Doe
                  </p>
                  <p className={cn('text-xs truncate', textVariants[variant], 'opacity-70')}>
                    Restaurant Owner
                  </p>
                </div>
              </>
            )}
            <Button
              variant="ghost"
              size="sm"
              className={cn(
                'h-8 w-8 p-0',
                variant === 'gradient' ? 'hover:bg-white/20 text-white' : '',
                isCollapsed && 'mx-auto'
              )}
            >
              <LogOut className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </TooltipProvider>
  )
}

// Mobile menu trigger
export function MobileSidebarTrigger() {
  const { setIsMobileOpen } = useSidebar()

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={() => setIsMobileOpen(true)}
      className="lg:hidden"
    >
      <Menu className="h-5 w-5" />
    </Button>
  )
}
